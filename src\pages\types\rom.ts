// DATA

export const FACILITY_TYPES = [
  { name: "library", label: "Library", value: 214 },
  { name: "museum", label: "Museum", value: 221 },
  { name: "industrial_plant", label: "Industrial Plant", value: 248 },
  { name: "medical", label: "Medical", value: 251 },
  { name: "churches", label: "Churches", value: 258 },
  { name: "government", label: "Government", value: 261 },
  { name: "retail", label: "Retail", value: 269 },
  { name: "multiple_family_dwelling", label: "Multiple - Family Dwelling", value: 272 },
  { name: "office_space", label: "Office Space", value: 277 },
  { name: "tenant_space", label: "Tenant Space - only", value: 281 },
  { name: "warehouse_basic", label: "Warehouse, Basic", value: 288 },
  { name: "warehouse_distribution", label: "Warehouse, Distribution", value: 291 },
  { name: "warehouse_storage", label: "Warehouse, Storage", value: 295 },
  { name: "education", label: "Education", value: 300 },
  { name: "hospitality", label: "Hospitality", value: 305 },
] as const

export const CEILING_TYPES = [
  { name: "under_9", label: "Under 9'", value: 0 },
  { name: "9_to_12", label: "9' to 12'", value: -10 },
  { name: "13_to_20", label: "13' to 20'", value: -14 },
  { name: "21_to_40", label: "21' to 40'", value: -18 },
  { name: "41_to_50", label: "41' to 50'", value: -23 },
  { name: "over_51", label: "Over 51'", value: -29 },
] as const

export const CONTAMINANT_LEVELS = [
  { name: "very_light", label: "Very Light", value: -2 },
  { name: "light", label: "Light", value: -4 },
  { name: "moderate", label: "Moderate", value: -12 },
  { name: "moderate_heavy", label: "Moderate Heavy", value: -21 },
  { name: "heavy", label: "Heavy", value: -31 },
] as const

export const CONTENT_CONCENTRATIONS = [
  { name: "very_light", label: "Very Light", value: -2 },
  { name: "light", label: "Light", value: -7 },
  { name: "moderate", label: "Moderate", value: -14 },
  { name: "moderate_heavy", label: "Moderate Heavy", value: -23 },
  { name: "heavy", label: "Heavy", value: -34 },
] as const

export const CONFINED_SPACE = [
  { name: "yes", label: "Yes", value: -7 },
  { name: "no", label: "No", value: 0 },
] as const

export const CONTAINMENT = [
  { name: "yes", label: "Yes", value: -40 },
  { name: "no", label: "No", value: 0 },
] as const

export const DEBRIS_REMOVAL = [
  { name: "yes", label: "Yes", value: -12 },
  { name: "no", label: "No", value: 0 },
] as const

export const ELEVATOR_SYSTEM = [
  { name: "yes", label: "Yes", value: -13 },
  { name: "no", label: "No", value: 0 },
] as const

export const FALSE_AIR_SPACE = [
  { name: "yes", label: "Yes", value: -11 },
  { name: "no", label: "No", value: 0 },
] as const

export const HVAC = [
  { name: "yes", label: "Yes", value: -12 },
  { name: "no", label: "No", value: 0 },
] as const

export const NUMBER_OF_STORIES = [
  { name: "two_to_twenty", label: "Two to Twenty", value: -10 },
  { name: "twenty_one_to_forty", label: "Twenty-one to Forty", value: -13 },
  { name: "forty", label: "Forty+", value: -15 },
] as const

export const CAUSE_OF_LOSS = [
  { name: "fire", label: "Fire", value: -10 },
  { name: "mold", label: "Mold", value: -10 },
  { name: "water", label: "Water", value: -5 },
] as const

export const WATER_CATEGORY = [
  { name: "cat1", label: "Cat 1", value: 0 },
  { name: "cat2", label: "Cat 2", value: 0 },
  { name: "cat3", label: "Cat 3", value: 0 },
] as const

export const WATER_CLASS = [
  { name: "class1", label: "1", value: 0 },
  { name: "class2", label: "2", value: 0 },
  { name: "class3", label: "3", value: 0 },
] as const

export const ELEVATOR_INCLUDED_IN_SCOPE = [
  { name: "yes", label: "Yes", value: 0 },
  { name: "no", label: "No", value: 0 },
] as const

// DERIVED DATA TYPES

export type FacilityType = typeof FACILITY_TYPES[number]["name"];
export type CeilingType = typeof CEILING_TYPES[number]["name"];
export type ContaminantLevel = typeof CONTAMINANT_LEVELS[number]["name"];
export type ContentConcentration = typeof CONTENT_CONCENTRATIONS[number]["name"];
export type ConfinedSpace = typeof CONFINED_SPACE[number]["name"];
export type Containment = typeof CONTAINMENT[number]["name"];
export type DebrisRemoval = typeof DEBRIS_REMOVAL[number]["name"];
export type ElevatorSystem = typeof ELEVATOR_SYSTEM[number]["name"];
export type FalseAirSpace = typeof FALSE_AIR_SPACE[number]["name"];
export type Hvac = typeof HVAC[number]["name"];
export type NumberOfStories = typeof NUMBER_OF_STORIES[number]["name"];
export type CauseOfLoss = typeof CAUSE_OF_LOSS[number]["name"];
export type WaterCategory = typeof WATER_CATEGORY[number]["name"];
export type WaterClass = typeof WATER_CLASS[number]["name"];
export type ElevatorIncludedInScope = typeof ELEVATOR_INCLUDED_IN_SCOPE[number]["name"];
