export interface IROMCreatePayload {
  assignmentId: number;
  area: number;
  height: number
  duration: number;
  daysWorked: number;

  facilityType: string
  ceilingType: string
  contaminantLevel: string
  contentConcentration: string
  confinedSpace: string
  containment: string
  debrisRemoval: string
  elevatorSystem: string
  falseAirSpace: string
  hvac: string
  numberOfStories: string
  causeOfLoss: string
  waterCategory: string
  waterClass: string
  elevatorIncludedInScope: string
  laborEfficiency: number
}

export interface IROMUpdatePayload extends IROMCreatePayload {
  id: number;
  createdAt?: string;
  updatedAt?: string;
}