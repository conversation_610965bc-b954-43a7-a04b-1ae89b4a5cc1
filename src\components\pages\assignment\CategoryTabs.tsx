import { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LoadingSpinner } from '@/components/custom-ui/loading';
import { useToast } from '@/components/custom-ui/toast';
import { useBusinessContext } from '@/context/BusinessContext';
import type { IAssignment, IAssignmentCategory, IAssignmentCategoryUpdatePayload } from '@/types';
import { Save, Tag, Edit, X } from 'lucide-react';

interface CategoryTabsProps {
  assignmentId: number;
  categoryData?: IAssignmentCategory[];
  onUpdate?: (assignment: IAssignment) => void;
}

interface CategoryFormData {
  presentValue: number;
  asAnalyzedValue: number;
}

export function CategoryTabs({ assignmentId, categoryData = [], onUpdate }: CategoryTabsProps) {
  const [formData, setFormData] = useState<Record<number, CategoryFormData>>({});
  const [savingStates, setSavingStates] = useState<Record<number, boolean>>({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('');
  const [editingAsPresented, setEditingAsPresented] = useState<boolean>(false);
  const [editingAsAnalyzed, setEditingAsAnalyzed] = useState<boolean>(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<Record<number, boolean>>({
    ...categoryData.reduce((acc, cd) => {
      acc[cd.categoryId] = false;
      return acc;
    }, {} as Record<number, boolean>)
  });

  const {
    entities: { categories: contextCategories },
    fetchActions: { fetchEntity, fetchCategories },
    entityActions: { patchEntity },
  } = useBusinessContext();
  const { addToast } = useToast();

  useEffect(() => {
    const loadCategories = async () => {
      setLoading(true);
      try {
        await fetchCategories();
      } catch (error) {
        console.error('Error loading categories:', error);
        addToast({
          type: "error",
          title: "Failed to load categories",
          description: "Could not fetch available categories"
        });
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, [fetchCategories, addToast]);

  useEffect(() => {
    const initialFormData: Record<number, CategoryFormData> = {};

    contextCategories.forEach(category => {
      const existingData = categoryData.find(cd => cd.categoryId === category.id);
      initialFormData[category.id] = {
        presentValue: existingData?.presentValue || 0,
        asAnalyzedValue: existingData?.asAnalyzedValue || 0
      };
    });

    setFormData(initialFormData);
  }, [contextCategories, categoryData]);

  useEffect(() => {
    if (contextCategories.length > 0 && !activeTab) {
      setActiveTab(contextCategories[0].id.toString());
    }
  }, [contextCategories, activeTab]);

  const handleInputChange = (categoryId: number, field: keyof CategoryFormData, value: string) => {
    const numericValue = parseFloat(value) || 0;
    setFormData(prev => ({
      ...prev,
      [categoryId]: {
        ...prev[categoryId],
        [field]: numericValue
      }
    }));
  };

  const removeCategoryPriceChange = (categoryId: number, field: keyof CategoryFormData) => {
    setFormData(prev => ({
      ...prev,
      [categoryId]: {
        ...prev[categoryId],
        [field]: categoryData.find(cd => cd.categoryId === categoryId)?.[field] || 0
      }
    }));
  }

  const handleSaveCategory = async (categoryId: number) => {
    setSavingStates(prev => ({ ...prev, [categoryId]: true }));
    setHasUnsavedChanges(prev => ({ ...prev, [categoryId]: false }));
    try {
      const data = formData[categoryId];
      if (!data) {
        throw new Error('No data to save');
      }

      const payload: IAssignmentCategoryUpdatePayload = {
        assignmentID: assignmentId,
        categoryID: categoryId,
        presentValue: data.presentValue,
        asAnalyzedValue: data.asAnalyzedValue
      };

      const response = await patchEntity<IAssignmentCategoryUpdatePayload, IAssignmentCategory>(`/v1/categories/assignment`, payload);
      if (response.error) throw new Error(response.error || 'Failed to save category data');

      addToast({
        type: "success",
        title: "Category updated",
        description: `Successfully updated ${contextCategories.find(c => c.id === categoryId)?.name || 'category'} data`
      });

      updateCategoriesData();
    } catch (error) {
      console.error('Error saving category:', error);
      addToast({
        type: "error",
        title: "Save failed",
        description: error instanceof Error ? error.message : 'Failed to save category data'
      });
    } finally {
      setSavingStates(prev => ({ ...prev, [categoryId]: false }));
    }
  };

  const updateCategoriesData = async () => {
    const updatedAssignment = await fetchEntity<IAssignment>(`/v1/assignments/${assignmentId}`);
    if (updatedAssignment.error || !updatedAssignment.data) {
      console.error('Failed to fetch updated assignment:', updatedAssignment.error);
      addToast({
        type: "error",
        title: "Failed to fetch updated assignment",
        description: updatedAssignment.error || "Failed to fetch updated assignment. Please try again."
      });
      return
    }
    setFormData(updatedAssignment.data.categoryData?.reduce((acc, cd) => {
      acc[cd.categoryId] = {
        presentValue: cd.presentValue,
        asAnalyzedValue: cd.asAnalyzedValue
      };
      return acc;
    }, {} as Record<number, CategoryFormData>) || {});
    onUpdate?.(updatedAssignment.data);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (contextCategories.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-muted-foreground">No categories available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl">
          <div className="p-2 bg-primary/10 dark:bg-primary/20 rounded-lg">
            <Tag className="h-5 w-5 text-primary" />
          </div>
          Assignment Categories
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full bg-muted/30" style={{ gridTemplateColumns: `repeat(${contextCategories.length}, minmax(0, 1fr))` }}>
            {contextCategories.map(category => (
              <TabsTrigger
                key={category.id}
                value={category.id.toString()}
                className="flex-1 data-[state=active]:bg-background data-[state=active]:shadow-sm font-medium"
              >
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {contextCategories.sort((a, b) => a.name.localeCompare(b.name)).map(category => {
            const categoryFormData = formData[category.id];
            const isSaving = savingStates[category.id];

            return (
              <TabsContent key={category.id} value={category.id.toString()} className="mt-4">
                <div className="rounded-lg border bg-card/50 p-6">
                  <div className="space-y-6">
                    {/* Category Header */}
                    <div className="flex items-center justify-between border-b pb-3">
                      <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        {category.name} Values
                      </h3>
                      <div className="text-sm text-muted-foreground">
                        Category ID: {category.id}
                      </div>
                    </div>

                    {/* Compact Form Layout */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Present Value */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label htmlFor={`present-value-${category.id}`} className="flex items-center gap-2 text-sm font-medium">
                            As Presented Amount
                          </Label>
                          <div className="text-xs text-muted-foreground">
                            Current
                          </div>
                        </div>
                        <div className="space-y-2">
                          {
                            editingAsPresented ? (
                              <div className='flex items-center justify-between gap-2'>
                                <Button
                                  onClick={
                                    () => {
                                      setEditingAsPresented(false)
                                      setHasUnsavedChanges(prev => ({ ...prev, [category.id]: false }))
                                      removeCategoryPriceChange(category.id, 'presentValue')
                                    }}
                                  variant="destructive"
                                  size="sm"
                                  className="whitespace-nowrap"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                                <Input
                                  id={`present-value-${category.id}`}
                                  type="number"
                                  disabled={isSaving}
                                  step="0.01"
                                  min="0"
                                  value={categoryFormData?.presentValue || 0}
                                  onChange={(e) => {
                                    handleInputChange(category.id, 'presentValue', e.target.value)
                                    setHasUnsavedChanges(prev => ({ ...prev, [category.id]: true }))
                                  }}
                                  placeholder="0.00"
                                  className="text-right font-mono text-base h-10"
                                />

                              </div>
                            ) : (
                              <div className='flex items-center justify-between gap-2'>
                                <Button
                                  onClick={() => { setEditingAsPresented(true) }}
                                  variant="outline"
                                  size="sm"
                                  className="whitespace-nowrap"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <div className="text-sm w-full font-medium text-success bg-success/5 px-3 py-1.5 rounded-md border border-success/20">
                                  {formatCurrency(categoryFormData?.presentValue || 0)}
                                </div>
                              </div>
                            )
                          }
                        </div>
                      </div>

                      {/* As Analyzed Value */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label htmlFor={`analyzed-value-${category.id}`} className="flex items-center gap-2 text-sm font-medium">
                            As Analyzed Amount
                          </Label>
                          <div className="text-xs text-muted-foreground">
                            Analyzed
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div>
                            {
                              editingAsAnalyzed ? (
                                <div className='flex items-center justify-between gap-2'>
                                  <Button
                                    onClick={() => {
                                      setEditingAsAnalyzed(false)
                                      setHasUnsavedChanges(prev => ({ ...prev, [category.id]: false }))
                                      removeCategoryPriceChange(category.id, 'asAnalyzedValue')
                                    }}
                                    variant="destructive"
                                    size="sm"
                                    className="whitespace-nowrap"
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                  <Input
                                    id={`analyzed-value-${category.id}`}
                                    type="number"
                                    disabled={isSaving}
                                    step="0.01"
                                    min="0"
                                    value={categoryFormData?.asAnalyzedValue || 0}
                                    onChange={(e) => {
                                      handleInputChange(category.id, 'asAnalyzedValue', e.target.value)
                                      setHasUnsavedChanges(prev => ({ ...prev, [category.id]: true }))
                                    }}
                                    placeholder="0.00"
                                    className="text-right font-mono text-base h-10"
                                  />
                                </div>
                              ) : (
                                <div className='flex items-center justify-between gap-2'>
                                  <Button
                                    onClick={() => setEditingAsAnalyzed(true)}
                                    variant="outline"
                                    size="sm"
                                    className="whitespace-nowrap"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <div className="text-sm w-full font-medium text-info bg-info/5 px-3 py-1.5 rounded-md border border-info/20">
                                    {formatCurrency(categoryFormData?.asAnalyzedValue || 0)}
                                  </div>
                                </div>
                              )
                            }
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Value Comparison */}
                    {categoryFormData && (categoryFormData.presentValue > 0 || categoryFormData.asAnalyzedValue > 0) && (
                      <div className="bg-muted/30 rounded-lg p-4 border">
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-medium text-muted-foreground">Difference:</span>
                          <span className={`font-semibold ${(categoryFormData.asAnalyzedValue - categoryFormData.presentValue) >= 0
                            ? 'text-success'
                            : 'text-destructive'
                            }`}>
                            {formatCurrency(categoryFormData.asAnalyzedValue - categoryFormData.presentValue)}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Action Button */}
                    <div className="flex justify-end pt-2">
                      <Button
                        onClick={() => {
                          handleSaveCategory(category.id)
                          setEditingAsAnalyzed(false)
                          setEditingAsPresented(false)
                          window.location.reload()
                        }}
                        disabled={isSaving || !hasUnsavedChanges[category.id]}
                        className="gap-2 min-w-[140px]"
                        size="default"
                      >
                        {isSaving ? (
                          <>
                            <LoadingSpinner size="sm" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4" />
                            Save Category
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            );
          })}
        </Tabs>
      </CardContent>
    </Card>
  );
}