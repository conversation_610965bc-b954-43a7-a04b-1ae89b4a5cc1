import { z } from "zod";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useMemo } from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import type { IROM } from "@/types/domain";
import type { IROMCreatePayload, IROMUpdatePayload } from "@/types";
import { useBusinessContext } from "@/context/BusinessContext";
import { useToast } from "@/components/custom-ui/toast";
import {
  CAUSE_OF_LOSS,
  CEILING_TYPES,
  CONFINED_SPACE,
  CONTAINMENT,
  CONTAMINANT_LEVELS,
  CONTENT_CONCENTRATIONS,
  DEBRIS_REMOVAL,
  ELEVATOR_SYSTEM,
  FACILITY_TYPES,
  FALSE_AIR_SPACE,
  HVAC,
  NUMBER_OF_STORIES,
  WATER_CATEGORY,
  WATER_CLASS,
  ELEVATOR_INCLUDED_IN_SCOPE,
} from "@/pages/types/rom";
import type {
  CauseOfLoss,
  CeilingType,
  ConfinedSpace,
  Containment,
  ContaminantLevel,
  ContentConcentration,
  DebrisRemoval,
  ElevatorSystem,
  FacilityType,
  FalseAirSpace,
  Hvac,
  NumberOfStories,
  WaterCategory,
  WaterClass,
  ElevatorIncludedInScope,
} from "@/pages/types/rom";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export const romFormSchema = z.object({
  id: z.number().optional(),
  assignmentId: z.coerce.number(),
  //1. Project details
  area: z.coerce.number(),
  height: z.coerce.number(),
  duration: z.coerce.number(),
  daysWorked: z.coerce.number(),

  //2. Quotient calculation
  facilityType: z.enum(FACILITY_TYPES.map(type => type.name) as [FacilityType, ...FacilityType[]]),
  ceilingType: z.enum(CEILING_TYPES.map(type => type.name) as [CeilingType, ...CeilingType[]]),
  contaminantLevel: z.enum(CONTAMINANT_LEVELS.map(level => level.name) as [ContaminantLevel, ...ContaminantLevel[]]),
  contentConcentration: z.enum(CONTENT_CONCENTRATIONS.map(concentration => concentration.name) as [ContentConcentration, ...ContentConcentration[]]),
  confinedSpace: z.enum(CONFINED_SPACE.map(space => space.name) as [ConfinedSpace, ...ConfinedSpace[]]),
  containment: z.enum(CONTAINMENT.map(containment => containment.name) as [Containment, ...Containment[]]),
  debrisRemoval: z.enum(DEBRIS_REMOVAL.map(removal => removal.name) as [DebrisRemoval, ...DebrisRemoval[]]),
  elevatorSystem: z.enum(ELEVATOR_SYSTEM.map(system => system.name) as [ElevatorSystem, ...ElevatorSystem[]]),
  falseAirSpace: z.enum(FALSE_AIR_SPACE.map(space => space.name) as [FalseAirSpace, ...FalseAirSpace[]]),
  hvac: z.enum(HVAC.map(hvac => hvac.name) as [Hvac, ...Hvac[]]),
  numberOfStories: z.enum(NUMBER_OF_STORIES.map(stories => stories.name) as [NumberOfStories, ...NumberOfStories[]]),
  causeOfLoss: z.enum(CAUSE_OF_LOSS.map(cause => cause.name) as [CauseOfLoss, ...CauseOfLoss[]]),
  waterCategory: z.enum(WATER_CATEGORY.map(category => category.name) as [WaterCategory, ...WaterCategory[]]),
  waterClass: z.enum(WATER_CLASS.map(waterClass => waterClass.name) as [WaterClass, ...WaterClass[]]),
  elevatorIncludedInScope: z.enum(ELEVATOR_INCLUDED_IN_SCOPE.map(included => included.name) as [ElevatorIncludedInScope, ...ElevatorIncludedInScope[]]),
  laborEfficiency: z.coerce
    .number()
    .min(1, { message: "Must be greater than 0" })
    .max(24, { message: "Must be less than 25" })
    .refine(val => !Number.isNaN(val), { message: "Must be a number" }),
})

interface ROMFormProps {
  initialData?: IROM;
  assignmentId: number;
  onCancel: () => void;
  mode?: "create" | "edit";
  fetchAssignment: () => void;
}

export function ROMForm({ initialData, assignmentId, onCancel, mode = "create", fetchAssignment }: ROMFormProps) {

  const form = useForm<z.infer<typeof romFormSchema>>({
    resolver: zodResolver(romFormSchema),
    defaultValues: {
      id: initialData?.id || 0,
      assignmentId: assignmentId,
      area: initialData?.area || 0,
      height: initialData?.height || 0,
      duration: initialData?.duration || 0,
      daysWorked: initialData?.daysWorked || 0,

      facilityType: initialData?.facilityType as FacilityType || "",
      ceilingType: initialData?.ceilingType as CeilingType || "",
      contaminantLevel: initialData?.contaminantLevel as ContaminantLevel || "",
      contentConcentration: initialData?.contentConcentration as ContentConcentration || "",
      confinedSpace: initialData?.confinedSpace as ConfinedSpace || "",
      containment: initialData?.containment as Containment || "",
      debrisRemoval: initialData?.debrisRemoval as DebrisRemoval || "",
      elevatorSystem: initialData?.elevatorSystem as ElevatorSystem || "",
      falseAirSpace: initialData?.falseAirSpace as FalseAirSpace || "",
      hvac: initialData?.hvac as Hvac || "",
      numberOfStories: initialData?.numberOfStories as NumberOfStories || "",
      causeOfLoss: initialData?.causeOfLoss as CauseOfLoss || "",
      waterCategory: initialData?.waterCategory as WaterCategory || "",
      waterClass: initialData?.waterClass as WaterClass || "",
      elevatorIncludedInScope: initialData?.elevatorIncludedInScope as ElevatorIncludedInScope || "",
      laborEfficiency: initialData?.laborEfficiency || 0,
    },
  })

  const {
    entityActions: {
      createEntity,
      updateEntity
    },
  } = useBusinessContext();
  const { addToast } = useToast();

  const {
    area = 0,
    height = 0,
    duration = 0,

    facilityType = "" as FacilityType,
    ceilingType = "" as CeilingType,
    contaminantLevel = "" as ContaminantLevel,
    contentConcentration = "" as ContentConcentration,
    confinedSpace = "" as ConfinedSpace,
    containment = "" as Containment,
    debrisRemoval = "" as DebrisRemoval,
    elevatorSystem = "" as ElevatorSystem,
    falseAirSpace = "" as FalseAirSpace,
    hvac = "" as Hvac,
    numberOfStories = "" as NumberOfStories,
    causeOfLoss = "" as CauseOfLoss,
    waterCategory = "" as WaterCategory,
    waterClass = "" as WaterClass,
    elevatorIncludedInScope = "" as ElevatorIncludedInScope,
    laborEfficiency = 0,
    daysWorked = 0,
  } = useWatch({ control: form.control });

  const summary = useMemo(() => {

    const lookup = <T extends { name: string; value: number }>(
      arr: readonly T[],
      key: string
    ) => arr.find((o) => o.name === key as any)?.value ?? 0;

    const quotient =
      lookup(FACILITY_TYPES, facilityType) +
      lookup(CEILING_TYPES, ceilingType) +
      lookup(CONTAMINANT_LEVELS, contaminantLevel) +
      lookup(CONTENT_CONCENTRATIONS, contentConcentration) +
      lookup(CONFINED_SPACE, confinedSpace) +
      lookup(CONTAINMENT, containment) +
      lookup(DEBRIS_REMOVAL, debrisRemoval) +
      lookup(ELEVATOR_SYSTEM, elevatorSystem) +
      lookup(FALSE_AIR_SPACE, falseAirSpace) +
      lookup(HVAC, hvac) +
      lookup(NUMBER_OF_STORIES, numberOfStories) +
      lookup(CAUSE_OF_LOSS, causeOfLoss) +
      lookup(WATER_CATEGORY, waterCategory) +
      lookup(WATER_CLASS, waterClass) +
      lookup(ELEVATOR_INCLUDED_IN_SCOPE, elevatorIncludedInScope);

    const cubicFootage = height * area;
    const totalHourlySqftPerHour = quotient / laborEfficiency;
    const totalNumberOfWeeks = duration / daysWorked
    const workerLaborEfficiencyPerShift = totalHourlySqftPerHour * laborEfficiency;
    const sqftProductionPerDay = area / duration;
    const requiredProductionWorkers = (sqftProductionPerDay / workerLaborEfficiencyPerShift);

    return { quotient, cubicFootage, totalHourlySqftPerHour, totalNumberOfWeeks, workerLaborEfficiencyPerShift, sqftProductionPerDay, requiredProductionWorkers };
  }, [
    area,
    height,
    duration,
    facilityType,
    ceilingType,
    contaminantLevel,
    contentConcentration,
    confinedSpace,
    containment,
    debrisRemoval,
    elevatorSystem,
    falseAirSpace,
    hvac,
    numberOfStories,
    causeOfLoss,
    waterCategory,
    waterClass,
    elevatorIncludedInScope,
    laborEfficiency,
    daysWorked,
  ]);

  const handleSubmit = (data: z.infer<typeof romFormSchema>) => {
    if (mode === "create") {
      handleCreate(data);
    } else {
      handleEdit(data);
    }
  }

  const handleCreate = async (data: z.infer<typeof romFormSchema>) => {
    const payload: IROMCreatePayload = {
      assignmentId: data.assignmentId,
      area: data.area,
      duration: data.duration,
      height: data.height,
      daysWorked: data.daysWorked,

      facilityType: data.facilityType,
      ceilingType: data.ceilingType,
      contaminantLevel: data.contaminantLevel,
      contentConcentration: data.contentConcentration,
      confinedSpace: data.confinedSpace,
      containment: data.containment,
      debrisRemoval: data.debrisRemoval,
      elevatorSystem: data.elevatorSystem,
      falseAirSpace: data.falseAirSpace,
      hvac: data.hvac,
      numberOfStories: data.numberOfStories,
      causeOfLoss: data.causeOfLoss,
      waterCategory: data.waterCategory,
      waterClass: data.waterClass,
      elevatorIncludedInScope: data.elevatorIncludedInScope,
      laborEfficiency: data.laborEfficiency,
    };

    const response = await createEntity<IROMCreatePayload, IROM>("/v1/roms", payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "ROM creation failed",
        description: response.error || "Failed to create ROM"
      });
      return;
    }
    addToast({
      type: "success",
      title: "ROM created",
      description: "The ROM has been created successfully."
    })
    onCancel()
    fetchAssignment();
  }

  const handleEdit = async (data: z.infer<typeof romFormSchema>) => {
    if (!initialData || !data.id) return addToast({
      type: "error",
      title: "ROM update failed",
      description: "Failed to update ROM. ROM ID is missing."
    });

    const payload: IROMUpdatePayload = {
      id: data.id,
      assignmentId: data.assignmentId,
      area: data.area,
      height: data.height,
      duration: data.duration,
      daysWorked: data.daysWorked,

      facilityType: data.facilityType,
      ceilingType: data.ceilingType,
      contaminantLevel: data.contaminantLevel,
      contentConcentration: data.contentConcentration,
      confinedSpace: data.confinedSpace,
      containment: data.containment,
      debrisRemoval: data.debrisRemoval,
      elevatorSystem: data.elevatorSystem,
      falseAirSpace: data.falseAirSpace,
      hvac: data.hvac,
      numberOfStories: data.numberOfStories,
      causeOfLoss: data.causeOfLoss,
      waterCategory: data.waterCategory,
      waterClass: data.waterClass,
      elevatorIncludedInScope: data.elevatorIncludedInScope,
      laborEfficiency: data.laborEfficiency,
    };

    const response = await updateEntity<IROMUpdatePayload, IROM>(`/v1/roms/${initialData.id}`, payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "ROM update failed",
        description: response.error || "Failed to update ROM"
      });
      return;
    }
    addToast({
      type: "success",
      title: "ROM updated",
      description: "The ROM has been updated successfully."
    })
    onCancel()
    fetchAssignment();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 w-full">
        <Accordion type="multiple" defaultValue={["project-details", "quotient", "production_rate", "worker_efficiency", "est_timeline", "hourly_sqft"]}>
          <AccordionItem value="project-details">
            <AccordionTrigger>Project Details</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <FormField
                  control={form.control}
                  name="area"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Affected Square Feet</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter area" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ceiling Height</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter height" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Est. Working Days</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter duration" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="quotient">
            <AccordionTrigger>Quotient Calculation</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="facilityType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Facility Type</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select facility type" />
                          </SelectTrigger>
                          <SelectContent>
                            {FACILITY_TYPES.map((facilityType) => (
                              <SelectItem key={facilityType.name} value={facilityType.name}>
                                {facilityType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="ceilingType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ceiling Type</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select ceiling type" />
                          </SelectTrigger>
                          <SelectContent>
                            {CEILING_TYPES.map((ceilingType) => (
                              <SelectItem key={ceilingType.name} value={ceilingType.name}>
                                {ceilingType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contaminantLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contaminant Level</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select contaminant level" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONTAMINANT_LEVELS.map((contaminantLevel) => (
                              <SelectItem key={contaminantLevel.name} value={contaminantLevel.name}>
                                {contaminantLevel.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contentConcentration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Content Concentration</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select content concentration" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONTENT_CONCENTRATIONS.map((contentConcentration) => (
                              <SelectItem key={contentConcentration.name} value={contentConcentration.name}>
                                {contentConcentration.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="confinedSpace"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confined Space</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select confined space" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONFINED_SPACE.map((confinedSpace) => (
                              <SelectItem key={confinedSpace.name} value={confinedSpace.name}>
                                {confinedSpace.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="containment"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Containment</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select containment" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONTAINMENT.map((containment) => (
                              <SelectItem key={containment.name} value={containment.name}>
                                {containment.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="debrisRemoval"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Debris Removal</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select debris removal" />
                          </SelectTrigger>
                          <SelectContent>
                            {DEBRIS_REMOVAL.map((removal) => (
                              <SelectItem key={removal.name} value={removal.name}>
                                {removal.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="elevatorSystem"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Elevator System</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select elevator system" />
                          </SelectTrigger>
                          <SelectContent>
                            {ELEVATOR_SYSTEM.map((system) => (
                              <SelectItem key={system.name} value={system.name}>
                                {system.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="falseAirSpace"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>False Air Space</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select false air space" />
                          </SelectTrigger>
                          <SelectContent>
                            {FALSE_AIR_SPACE.map((space) => (
                              <SelectItem key={space.name} value={space.name}>
                                {space.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="hvac"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>HVAC</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select HVAC" />
                          </SelectTrigger>
                          <SelectContent>
                            {HVAC.map((hvac) => (
                              <SelectItem key={hvac.name} value={hvac.name}>
                                {hvac.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="numberOfStories"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Stories</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select number of stories" />
                          </SelectTrigger>
                          <SelectContent>
                            {NUMBER_OF_STORIES.map((stories) => (
                              <SelectItem key={stories.name} value={stories.name}>
                                {stories.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="causeOfLoss"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cause of Loss</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select cause of loss" />
                          </SelectTrigger>
                          <SelectContent>
                            {CAUSE_OF_LOSS.map((cause) => (
                              <SelectItem key={cause.name} value={cause.name}>
                                {cause.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="waterCategory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Water Category</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select water category" />
                          </SelectTrigger>
                          <SelectContent>
                            {WATER_CATEGORY.map((category) => (
                              <SelectItem key={category.name} value={category.name}>
                                {category.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="waterClass"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Water Class</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select water class" />
                          </SelectTrigger>
                          <SelectContent>
                            {WATER_CLASS.map((waterClass) => (
                              <SelectItem key={waterClass.name} value={waterClass.name}>
                                {waterClass.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="elevatorIncludedInScope"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Elevator Included In Scope</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select elevator included in scope" />
                          </SelectTrigger>
                          <SelectContent>
                            {ELEVATOR_INCLUDED_IN_SCOPE.map((included) => (
                              <SelectItem key={included.name} value={included.name}>
                                {included.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
          <div className="grid grid-cols-2 gap-8">
            <AccordionItem value="production_rate">
              <AccordionTrigger>Hourly Sqft Production Rate</AccordionTrigger>
              <AccordionContent>
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Daily Labor Efficiency (Quotient)</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {summary.quotient}
                  </p>
                </div>
                <FormField
                  control={form.control}
                  name="laborEfficiency"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between items-center">
                        <FormLabel className="text-sm font-medium text-muted-foreground">Hours Per Day (Labor Efficiency)</FormLabel>
                        <FormControl className="w-[70px]">
                          <Input type="number" placeholder="Enter labor efficiency" min={1} max={24} {...field} />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Total Hourly sqft Per Hour</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {
                      summary.totalHourlySqftPerHour.toFixed(2)
                    }
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="worker_efficiency">
              <AccordionTrigger>Production Worker Efficiency</AccordionTrigger>
              <AccordionContent>
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Working Shift (hrs)</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {form.getValues("laborEfficiency")}
                  </p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Total Hourly sqft Per Hour</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {
                      summary.totalHourlySqftPerHour.toFixed(2)
                    }
                  </p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Worker Labor Efficiency Per Shift</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {
                      summary.workerLaborEfficiencyPerShift.toFixed(2)
                    }
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="est_timeline">
              <AccordionTrigger>Estimated Timeline</AccordionTrigger>
              <AccordionContent>
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Total Working Days</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {
                      form.getValues("duration")
                    }
                  </p>
                </div>
                <FormField
                  control={form.control}
                  name="daysWorked"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between items-center">
                        <FormLabel className="text-sm font-medium text-muted-foreground">Days Worked Per Week</FormLabel>
                        <FormControl className="w-[70px]">
                          <Input type="number" placeholder="Enter # of days worked per week" {...field} />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Total # of Weeks</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {
                      summary.totalNumberOfWeeks.toFixed(2)
                    }
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="hourly_sqft">
              <AccordionTrigger>Hourly Sqft Production Rate</AccordionTrigger>
              <AccordionContent>
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">SQFT Production Per Day</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {
                      summary.sqftProductionPerDay.toFixed(2)
                    }
                  </p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Worker Labor Efficiency Per Shift</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {
                      summary.workerLaborEfficiencyPerShift.toFixed(2)
                    }
                  </p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm font-medium text-muted-foreground">Required Production Workers</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {
                      Math.ceil(summary.requiredProductionWorkers)
                    }
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>
          </div>
        </Accordion>
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
          >
            Save
          </Button>
        </div>
      </form>
    </Form >
  )
}
