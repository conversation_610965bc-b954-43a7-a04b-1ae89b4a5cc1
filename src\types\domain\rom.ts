export interface IROM {
  id: number;
  assignmentId: number;
  area: number;
  duration: number;
  height: number;
  daysWorked: number;

  facilityType: string;
  ceilingType: string;
  contaminantLevel: string;
  contentConcentration: string;
  confinedSpace: string;
  containment: string;
  debrisRemoval: string;
  elevatorSystem: string;
  falseAirSpace: string;
  hvac: string;
  numberOfStories: string;
  causeOfLoss: string;
  waterCategory: string;
  waterClass: string;
  elevatorIncludedInScope: string;
  laborEfficiency: number;

  createdAt: string;
  updatedAt: string;
}