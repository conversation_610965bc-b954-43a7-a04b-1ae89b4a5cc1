import { useBusinessContext } from "@/context/BusinessContext";
import type { IReport, ILaborDiscrepancy } from "@/types/domain";
import { useState, useEffect } from "react";
import { useToast } from "@/components/custom-ui/toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/custom-ui/badge";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, FileText, Calendar, Clock } from "lucide-react";

export function ReportView({ assignmentId }: { assignmentId: number }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [report, setReport] = useState<IReport | null>(null);

  const {
    fetchActions: { fetchEntity },
  } = useBusinessContext();

  const { addToast } = useToast();

  useEffect(() => {
    fetchReport();
  }, [assignmentId]);

  const fetchReport = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchEntity<IReport>(`/v1/reports/last-assignment/${assignmentId}`);

      if (response.data) {
        setReport(response.data);
      } else if (response.error) {
        setError(response.error);
      }
    } catch (error) {
      setError(error as string);
      addToast({
        type: 'error',
        title: 'Error fetching report',
        description: 'No report found'
      });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center gap-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-muted-foreground">Loading report...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !report) {
    return (
      <Card className="border-destructive/50">
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle size={48} className="text-destructive mb-4" />
          <h3 className="font-medium text-lg mb-2">No Report Found</h3>
          <p className="text-muted-foreground text-sm mb-4">
            No report was found for assignment #{assignmentId}.
          </p>
          <button
            onClick={fetchReport}
            className="text-primary hover:text-primary/80 text-sm font-medium"
          >
            Try again
          </button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Report Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText size={20} />
              Report Details
            </CardTitle>
            <Badge variant="outline">ID: {report.id}</Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Assignment ID</h4>
              <p className="text-sm font-mono">{report.assignmentId}</p>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground mb-1">Total Discrepancies</h4>
              <p className="text-sm font-semibold">{report.laborDiscrepancies.length}</p>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Calendar size={16} className="text-muted-foreground" />
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Created</h4>
                <p className="text-sm">{formatDate(report.createdAt)}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Clock size={16} className="text-muted-foreground" />
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Last Updated</h4>
                <p className="text-sm">{formatDate(report.updatedAt)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {
        report.laborDiscrepancies.length === 0 ? (
          <Card className="border-dashed">
            <CardContent className="flex flex-col items-center justify-center py-8 text-center">
              <FileText size={48} className="text-muted-foreground mb-4" />
              <h3 className="font-medium text-lg mb-2">No Labor Discrepancies</h3>
              <p className="text-muted-foreground text-sm">
                This report contains no labor discrepancies.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Labor Discrepancies</h3>
              <Badge variant="secondary">
                {report.laborDiscrepancies.length} {report.laborDiscrepancies.length === 1 ? 'item' : 'items'}
              </Badge>
            </div>

            <div className="space-y-4">
              {report.laborDiscrepancies.map((discrepancy, index) => (
                <LaborDiscrepancyItem
                  key={discrepancy.id}
                  discrepancy={discrepancy}
                  index={index}
                />
              ))}
            </div>
          </div>
        )
      }

    </div>
  )
}

function LaborDiscrepancyItem({ discrepancy, index }: { discrepancy: ILaborDiscrepancy; index: number }) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className="border-l-4 border-l-warning">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            <AlertCircle size={16} className="text-warning" />
            Discrepancy #{index + 1}
          </CardTitle>
          <Badge variant="outline" className="text-xs">
            ID: {discrepancy.id}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium text-sm text-muted-foreground mb-2">Description</h4>
          <p className="text-sm leading-relaxed">{discrepancy.discrepancy}</p>
        </div>

        {discrepancy.observations && (
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Observations</h4>
            <p className="text-sm leading-relaxed text-muted-foreground">
              {discrepancy.observations}
            </p>
          </div>
        )}

        <Separator />

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar size={12} />
            <span>Created: {formatDate(discrepancy.createdAt)}</span>
          </div>
          {discrepancy.updatedAt !== discrepancy.createdAt && (
            <div className="flex items-center gap-1">
              <Clock size={12} />
              <span>Updated: {formatDate(discrepancy.updatedAt)}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}